﻿using System.Security.Claims;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.Client.CreateClient;
using DataVenia.Modules.Users.Domain.SharedModels;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace DataVenia.Modules.Users.Presentation.Client;
internal sealed class CreateClient : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("/offices/{officeId}/clients", async (Guid officeId, CreateClientRequest request, ClaimsPrincipal claims, ISender sender) =>
        {
            Result<Guid> result = await sender.Send(new CreateClientCommand(
                officeId,
                claims.GetUserId(),
                request.Name,
                request.Cpf,
                request.Email
                ));

            return result.Match(id => Results.Ok(new { Id = id }), ApiResults.Problem);
        })
        .RequireAuthorization("office:clients:create")
        .WithTags(Tags.Client);
    }

}
public sealed record CreateClientRequest
{
    public string Name { get; init; }
    public string Cpf { get; init; }
    public string? Email { get; init; }
}
