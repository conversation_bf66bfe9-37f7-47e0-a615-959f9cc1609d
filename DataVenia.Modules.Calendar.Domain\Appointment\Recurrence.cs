﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Calendar.Domain.Appointments;

public sealed class Recurrence
{
    private readonly List<DayOfWeek> _daysOfWeek = [];
    private readonly List<int> _daysOfMonth = [];

    public Guid Id { get; private set; }
    public RecurrenceFrequency Frequency { get; private set; }
    public DateOnly StartDate { get; private set; }
    public DateOnly EndDate { get; private set; }
    public TimeOnly StartTime { get; private set; }
    public TimeOnly EndTime { get; private set; }
    public IReadOnlyCollection<DayOfWeek> DaysOfWeek => _daysOfWeek.ToList();
    public IReadOnlyCollection<int> DaysOfMonth => _daysOfMonth.ToList();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    private Recurrence() { }
    public static Result<Recurrence> Create(RecurrenceFrequency frequency, List<DayOfWeek>? daysOfWeek, List<int>? daysOfMonth, DateOnly startDate, DateOnly endDate, TimeOnly startTime, TimeOnly endTime)
    {
        if (startDate > endDate)
            return Result.Failure<Recurrence>(new Error("Invalid.Range", "Start date can't be after end date.", ErrorType.Validation));

        if(startTime > endTime)
            return Result.Failure<Recurrence>(new Error("Invalid.Range", "Start time can't be after end time.", ErrorType.Validation));

        Recurrence recurrence = new()
        {
            Id = Guid.NewGuid(),
            Frequency = frequency,
            StartDate = startDate,
            EndDate = endDate,
            StartTime = startTime,
            EndTime = endTime,
            CreatedAt = DateTime.UtcNow
        };


        if (frequency == RecurrenceFrequency.Weekly)
        {
            if (daysOfWeek is null || daysOfWeek.Count == 0)
                return Result.Failure<Recurrence>(new Error("Invalid.Days", "Days of week need to be specified when frequency is Weekly or Once", ErrorType.Validation));

            recurrence._daysOfWeek.AddRange(daysOfWeek);
            recurrence._daysOfMonth.Clear();
        }
        else if(frequency == RecurrenceFrequency.Monthly)
        {
            if(daysOfMonth is null || daysOfMonth.Count == 0)
                return Result.Failure<Recurrence>(new Error("Invalid.Days", "Days of month need to be specified when frequency is Monthly or Once", ErrorType.Validation));
            
            recurrence._daysOfMonth.AddRange(daysOfMonth);
            recurrence._daysOfWeek.Clear();
        }
        else if(frequency == RecurrenceFrequency.Once)
        {
            recurrence._daysOfMonth.Clear();
            recurrence._daysOfWeek.Clear();
        }

        return recurrence;
    }
}
