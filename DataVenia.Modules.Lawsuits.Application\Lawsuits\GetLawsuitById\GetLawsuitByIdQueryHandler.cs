﻿using System.Text.Json;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
using DataVenia.Modules.Lawsuits.Domain.LawsuitParties;
using DataVenia.Modules.Lawsuits.Domain.LawsuitParty;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuitById;

internal sealed class GetLawsuitByIdQueryHandler(ILawsuitRepository lawsuitRepository
    // , ILawsuitPartyRepository lawsuitPartyRepository
    )
    : IQueryHandler<GetLawsuitByIdQuery, EnrichedLawsuitResponse?>
{
    public async Task<Result<EnrichedLawsuitResponse?>> Handle(GetLawsuitByIdQuery request, CancellationToken cancellationToken)
    {
        Result<IReadOnlyCollection<LawsuitResponse>> lawsuits = await lawsuitRepository.GetLawsuitsAsync(request.userId, request.officeId, request.lawsuitId, cancellationToken);

        if(lawsuits.IsFailure)
            return Result.Failure<EnrichedLawsuitResponse?>(lawsuits.Error);
        
        LawsuitResponse? lawsuit = lawsuits.Value.FirstOrDefault();
        
        if(lawsuit == null)
            return Result.Failure<EnrichedLawsuitResponse?>(new Error("Not.Found", "The lawsuit was not found.", ErrorType.NotFound));

        // Result<IReadOnlyCollection<LawsuitParty>> lawsuitParties = await lawsuitPartyRepository.GetLawsuitPartiesAsync(request.lawsuitId, cancellationToken);

        // Map each LawsuitParty to a LawsuitPartyResponse
        // var lawsuitPartyResponses = lawsuitParties.Value
        //     .Select(lp => new LawsuitPartyResponse(
        //         Id: lp.Id,
        //         PartyId: lp.PartyId,
        //         LawsuitDataId: lp.LawsuitDataId,
        //         PartyType: lp.PartyType,
        //         IsClient: lp.IsClient
        //     ))
        //     .ToList();

        // 3) Create the EnrichedLawsuitResponse
        
        var enrichedLawsuit = new EnrichedLawsuitResponse(
            lawsuit.Id,
            lawsuit.Title,
            lawsuit.Cnj,
            lawsuit.FolderId,
            lawsuit.FolderName,
            lawsuit.LegalInstanceId,
            lawsuit.LawsuitTypeId,
            lawsuit.LegalCategoryId,
            lawsuit.LawsuitStatusId,
            // lawsuitPartyResponses,
            lawsuit.Court,
            lawsuit.CourtDivisionId,
            lawsuit.ForumId,
            lawsuit.CourtHref,
            lawsuit.CauseValue,
            lawsuit.ConvictionValue,
            lawsuit.Description,
            lawsuit.Observations,
            lawsuit.DistributedAt,
            lawsuit.CreatedAt,
            lawsuit.ResponsibleIds,
            lawsuit.Access,
            lawsuit.EvolvedFromCaseId,
            lawsuit.GroupingCaseId,
            lawsuit.IsInstanceCreatedByUser,
            lawsuit.MonitoringEnabled,
            JsonSerializer.Deserialize<IReadOnlyCollection<MonitoringHistoryEntry>>(lawsuit.MonitoringHistory) ?? []
        );

        return Result.Success<EnrichedLawsuitResponse?>(enrichedLawsuit);
    }
}
