﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.SharedModels;
using DataVenia.Modules.Users.Domain.Users;

namespace DataVenia.Modules.Users.Domain.Client;
public sealed class Client : Entity
{
    public Guid Id { get; private set; }
    public string Cpf { get; private set; }
    public string Name { get; private set; }
    public string? Email { get; private set; }
    public Guid OfficeId { get; private set; }
    public Office.Office Office { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    
    public ICollection<ClientCompany.ClientCompany> ClientCompanies { get; private set; } = new List<ClientCompany.ClientCompany>();
    
    
    public static Result<Client> Create(string cpf, string name, Guid officeId, Guid createdBy, string? email = null)
    {
        var client = new Client
        {
            Id = Guid.NewGuid(),
            Name = name,
            Cpf = cpf,
            OfficeId = officeId,
            CreatedBy = createdBy,
            CreatedAt = DateTime.UtcNow,
            Email = email
        };
        return client;
    }

    public void Update(string? name = null, string? cpf = null, string? email = null)
    {
        Name = name ?? Name;
        Cpf = cpf ?? Cpf;
        Email = email ?? Email;
        UpdatedAt = DateTime.UtcNow;
    }

    // private void UpdateContacts(List<Contact>? contacts)
    // {
    //     if (contacts is null)
    //         return;
    //
    //     _contacts.Clear();
    //     _contacts.AddRange(contacts);
    // }
    //
    // private void UpdateAddresses(List<Address>? addresses)
    // {
    //     if (addresses is null)
    //         return;
    //
    //     _addresses.Clear();
    //     _addresses.AddRange(addresses);
    // }
}
