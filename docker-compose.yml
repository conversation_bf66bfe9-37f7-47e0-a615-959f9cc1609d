version: "3.8"

volumes:
  database:
  identity:

services:
  datavenia.database:
    image: postgres:17.2-alpine3.21
    container_name: datavenia.Database
    # We'll create both databases (datavenia and keycloak) via an init script
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      # Setting POSTGRES_DB is optional if you're using init scripts.
      # If set, it just creates that DB by default. We'll create two DBs in the script anyway.
      - POSTGRES_DB=postgres
    ports:
      - 5432:5432
    volumes:
      - database:/var/lib/postgresql/data
      # Mount an init script from your host machine:
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  datavenia.api:
    container_name: datavenia.Api
    build:
      context: .
      dockerfile: DataVenia.Api/Dockerfile
    depends_on:
      - datavenia.database
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_HTTP_PORTS=8080
      # Example connection string pointing to "datavenia" DB
      - ConnectionStrings__Default=Host=datavenia.database;Port=5432;Database=datavenia;Username=postgres;Password=postgres
    ports:
      - 5001:8080
      - 5002:8081
      - 5163:5163

  datavenia.seq:
    image: datalust/seq:latest
    container_name: datavenia.Seq
    environment:
      - ACCEPT_EULA=Y
    ports:
      - 8082:5341
      - 8081:80

  datavenia.redis:
    image: redis:latest
    container_name: datavenia.Redis
    restart: always
    ports:
      - 6379:6379

  datavenia.identity:
    image: quay.io/keycloak/keycloak:latest
    container_name: datavenia.Identity
    depends_on:
      - datavenia.database
    command: start-dev --import-realm
    environment:
      - KC_HEALTH_ENABLED=true
      # Use PostgreSQL
      - KC_DB=postgres
      # Point to the "keycloak" DB we create in init.sql
      - KC_DB_URL=**************************************************
      - KC_DB_USERNAME=postgres
      - KC_DB_PASSWORD=postgres
      - KEYCLOAK_ADMIN=admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
    volumes:
      - identity:/opt/keycloak/data
      - ./.files/datavenia:/opt/keycloak/import
    ports:
      - 18080:8080
