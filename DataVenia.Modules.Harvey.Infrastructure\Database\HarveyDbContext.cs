﻿using DataVenia.Modules.Harvey.Application.Abstractions.Data;
using DataVenia.Modules.Harvey.Domain.LegalCategory;
using DataVenia.Modules.Harvey.Infrastructure.Forum;
using DataVenia.Modules.Harvey.Infrastructure.LegalInstance;
using DataVenia.Modules.Harvey.Infrastructure.PartyType;
using DataVenia.Modules.Harvey.Infrastructure.Status;
using Microsoft.EntityFrameworkCore;
using ActionDomain = DataVenia.Modules.Harvey.Domain.Action.LawsuitType;
using CourtDivisionDomain = DataVenia.Modules.Harvey.Domain.CourtDivision.CourtDivision;
using ForumDomain = DataVenia.Modules.Harvey.Domain.Forum.Forum;
using LegalInstanceDomain = DataVenia.Modules.Harvey.Domain.LegalInstance.LegalInstance;
using PartyTypeDomain = DataVenia.Modules.Harvey.Domain.PartyType.PartyType;
using StatusDomain = DataVenia.Modules.Harvey.Domain.LawsuitStatus.LawsuitStatus;
using LegalCategoryDomain = DataVenia.Modules.Harvey.Domain.LegalCategory.LegalCategory;
using DataVenia.Modules.Harvey.Infrastructure.LegalCategory;
namespace DataVenia.Modules.Harvey.Infrastructure.Database;
public sealed class HarveyDbContext(DbContextOptions<HarveyDbContext> options) : DbContext(options), IUnitOfWork
{
    internal DbSet<CourtDivisionDomain> CourtDivisions { get; set; }
    internal DbSet<ActionDomain> LawsuitTypes { get; set; }
    internal DbSet<ForumDomain> Forums{ get; set; }
    internal DbSet<LegalInstanceDomain> LegalInstances { get; set; }
    internal DbSet<StatusDomain> Status { get; set; }
    internal DbSet<PartyTypeDomain> PartyTypes { get; set; }
    internal DbSet<LegalCategoryDomain> LegalCategories { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasDefaultSchema(Schemas.Harvey);

        modelBuilder.ApplyConfiguration(new LegalInstanceConfiguration());
        modelBuilder.ApplyConfiguration(new LawsuitTypeConfiguration());
        modelBuilder.ApplyConfiguration(new CourtDivisionConfiguration());
        modelBuilder.ApplyConfiguration(new ForumConfiguration());
        modelBuilder.ApplyConfiguration(new LawsuitStatusConfiguration());
        modelBuilder.ApplyConfiguration(new PartyTypeConfiguration());
        modelBuilder.ApplyConfiguration(new LegalCategoryConfiguration());
    }
}
