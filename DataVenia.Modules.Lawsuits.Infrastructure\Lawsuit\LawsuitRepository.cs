﻿using System.Text;
using DataVenia.Common.Domain;
using Microsoft.EntityFrameworkCore;
using DataVenia.Modules.Lawsuits.Application.Lawsuits;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
using DataVenia.Modules.Lawsuits.Domain.DTOs;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using Microsoft.Extensions.Logging;
using Npgsql;

namespace DataVenia.Modules.Lawsuits.Infrastructure.Lawsuits;

internal sealed class LawsuitRepository(LawsuitsDbContext context, ILogger<LawsuitRepository> logger) : ILawsuitRepository
{
    public async Task<FluentResults.Result<IReadOnlyCollection<Lawsuit>>> GetLawsuitsByCnjAsync(string cnj, CancellationToken cancellationToken = default)
    {
        var lawsuit = await context.Lawsuits
            .Where(l => l.Cnj == cnj)
            .ToListAsync(cancellationToken);
        
        if (lawsuit is null)
            return FluentResults.Result.Fail<IReadOnlyCollection<Lawsuit>>("Lawsuit.NotFound");

        return FluentResults.Result.Ok((IReadOnlyCollection<Lawsuit>)lawsuit);
    }

    public async Task<Result<IReadOnlyCollection<LawsuitResponse>>> GetLawsuitsAsync(Guid userId, Guid officeId, Guid? lawsuitId = null,
        CancellationToken cancellationToken = default)
    {
        var sqlBuilder = new StringBuilder();
        sqlBuilder.AppendLine(@"
            SELECT 
    l.id AS Id,
    ld.title AS Title,
    l.cnj,
    ld.folder_id AS Folder_Id,
    f.name AS Folder_Name,
    ld.legal_instance_id,
    l.lawsuit_type_id,
    l.legal_category_id,
    ld.lawsuit_status_id,
    ld.court AS Court,
    ld.court_division_id,
    ld.forum_id,
    ld.court_href,
    ld.description,
    ld.observations,
    ld.cause_value,
    ld.conviction_value,
    l.distributed_at,
    ld.created_at,
    ld.evolved_from_case_id,
    ld.grouping_case_id,
    ld.is_instance_created_by_user,
    l.monitoring_enabled,
    l.monitoring_history,
    (
        SELECT ARRAY_AGG(lr.lawyer_id)
        FROM lawsuit.lawsuit_responsible lr
        WHERE lr.lawsuit_data_id = ld.id
    ) AS Responsible_Ids,
    ld.access
FROM lawsuit.lawsuit l
-- Get the latest lawsuit_data per lawsuit
INNER JOIN (
    SELECT DISTINCT ON (lawsuit_id) *
    FROM lawsuit.lawsuit_data ld
    WHERE ld.is_instance_created_by_user = true
    ORDER BY lawsuit_id, created_at DESC
) ld ON ld.lawsuit_id = l.id
LEFT JOIN lawsuit.folder f ON ld.folder_id = f.id
-- Aggregate responsibles from any lawsuit_data for the lawsuit
WHERE l.office_id = @officeId
        ");

        if (lawsuitId.HasValue)
        {
            sqlBuilder.AppendLine("AND l.id = @lawsuitId");
        }

        sqlBuilder.AppendLine(@"
            AND EXISTS (
    SELECT 1
    FROM lawsuit.lawsuit_responsible lr2
    INNER JOIN lawsuit.lawsuit_data ldr2 ON ldr2.id = lr2.lawsuit_data_id
    WHERE ldr2.lawsuit_id = l.id
      AND lr2.lawyer_id = @userId
)
        ");

        string sql = sqlBuilder.ToString();

        NpgsqlParameter[] parameters = new[]
        {
            new Npgsql.NpgsqlParameter("@officeId", officeId),
            new Npgsql.NpgsqlParameter("@userId", userId)
        };

        if (lawsuitId.HasValue)
        {
            // If lawsuitId is provided, add the corresponding parameter
            Array.Resize(ref parameters, parameters.Length + 1);
            parameters[parameters.Length - 1] = new Npgsql.NpgsqlParameter("@lawsuitId", lawsuitId);
        }
        try
        {

            List<LawsuitResponse> lawsuits = await context.Set<LawsuitResponse>()
                .FromSqlRaw(sql, parameters)
                .ToListAsync(cancellationToken);
        
            return lawsuits;
        }
        catch (Exception ex)
        {
            string amountOfLawsuits = lawsuitId == null ? "collection" : $"unit: {lawsuitId}";
            logger.LogError(ex, "Error occured while getting lawsuit. {AmountOfLawsuits}", amountOfLawsuits);
            return Result.Failure<IReadOnlyCollection<LawsuitResponse>>(new Error("Internal.Server.Error",
                "Something weird happened.", ErrorType.InternalServerError));
        }
    }

    public async Task<Lawsuit?> GetLawsuitByIdAsync(Guid lawsuitId, CancellationToken cancellationToken = default)
    {
        Lawsuit? lawsuit = await context.Lawsuits
            //.Include(l => l.LawsuitParties)            // Include intermediate table LawsuitParty
            //.ThenInclude(lp => lp.Party)              // Include Party data via LawsuitParty
            .Include(l => l.LawsuitDatas
                .OrderByDescending(ld => ld.CreatedAt)
                .Take(1))
            .ThenInclude(ld => ld.Responsibles)
            .Where(l => l.Id == lawsuitId)
            .FirstOrDefaultAsync(cancellationToken);  // Fetch the first matching record or return null if none found

        return lawsuit;
    }

    public void Insert(Lawsuit lawsuit)
    {
        context.Lawsuits.Add(lawsuit);
    }

    public void Update(Lawsuit lawsuit)
    {
        context.Lawsuits.Update(lawsuit);
    }
}
