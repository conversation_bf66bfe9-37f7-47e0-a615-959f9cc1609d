﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Domain.LawsuitParty;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using Microsoft.Extensions.Logging;
//using LawsuitPartyDomain = DataVenia.Modules.Lawsuits.Domain.IntermediateClasses.LawsuitParty;
namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.UpdateLawsuit;
public sealed class UpdateLawsuitCommandHandler(
    ILawsuitRepository lawsuitRepository,
    ILawsuitDataRepository lawsuitDataRepository,
    //ILawsuitPartyRepository lawsuitPartyRepository,
    IOfficeLawyerFacade officeLawyerFacade,
    IUnitOfWork unitOfWork,
    ILogger<UpdateLawsuitCommandHandler> logger) : ICommandHandler<UpdateLawsuitCommand>
{
    public async Task<Result> Handle(UpdateLawsuitCommand request, CancellationToken cancellationToken)
    {
        // Validate lawsuit existence
        Lawsuit? lawsuit = await lawsuitRepository.GetLawsuitByIdAsync(request.LawsuitId, cancellationToken);
        if (lawsuit == null)
            return Result.Failure(Error.NotFound("LawsuitNotFound", "The specified lawsuit does not exist."));
        
        if (!request.ResponsibleIds.Any())
            return Result.Failure(new Error("NoResponsible", "You must assign at least one responsible for the lawsuit", ErrorType.Validation));

        // Validate responsible users
        IReadOnlyCollection<OfficeLawyerDto> responsibles = await officeLawyerFacade.GetActiveByOfficeAndLawyers(request.OfficeId, request.ResponsibleIds);
        if (responsibles.Count != request.ResponsibleIds.Count)
            return Result.Failure(Error.Conflict("NotAuthorized", "There are inexistent users being attached to the lawsuit"));

        
        // vai ter que refatorar esse méotod pra pegar por instancia
        var latestLawsuitData = await lawsuitDataRepository.GetLatestOfInstanceByLawsuitIdAsync(request.LawsuitId, request.LegalInstanceId, cancellationToken);
        if (latestLawsuitData.IsFailed)
            return Result.Failure(new Error(
                latestLawsuitData.Errors.FirstOrDefault()?.Message ?? "Internal.Server.Error", "Could not find the lawsuit data",
                ErrorType.NotFound));
                
        // Create new lawsuitData record
        var newLawsuitData = LawsuitData.Create(
        request.Title,
        lawsuit.Cnj,
        lawsuit.Id,
        request.FolderId,
        request.LegalInstanceId,
        request.LawsuitStatusId,
        request.Court,
        request.CourtDivisionId,
        request.ForumId,
        request.CauseValue,
        request.ConvictionValue,
        request.CourtHref,
        request.Description,
        request.Observations,
        request.Access,
        // request.Parties,
        request.ResponsibleIds,
        request.EvolvedFromCaseId,
        request.GroupingCaseId,
        latestLawsuitData.Value.IsInstanceCreatedByUser
        );

        lawsuitDataRepository.Insert(newLawsuitData.Value);

        // Save changes
        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, nameof(UpdateLawsuitCommandHandler));
            return Result.Failure(new Error("Internal.Server.Error", "Something weird happened.", ErrorType.InternalServerError));
        }

        return Result.Success();
    }
}
