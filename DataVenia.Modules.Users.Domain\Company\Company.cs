﻿using DataVenia.Common.Domain;

namespace DataVenia.Modules.Users.Domain.Company;

public sealed class Company : Entity
{
    public Guid Id { get; private set; }
    public string Cnpj { get; private set; }
    public string Name { get; private set; }
    public Guid OfficeId { get; private set; }
    public Office.Office Office { get; private set; }
    public Guid CreatedBy { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    
    public ICollection<ClientCompany.ClientCompany> ClientCompanies { get; private set; } = new List<ClientCompany.ClientCompany>();
    
    public static Result<Company> Create(string cnpj, string name, Guid officeId, Guid createdBy)
    {
        var client = new Company
        {
            Id = Guid.NewGuid(),
            Name = name,
            Cnpj = cnpj,
            OfficeId = officeId,
            CreatedBy = createdBy,
            CreatedAt = DateTime.UtcNow
        };
        return client;
    }

    public void Update(string? name = null, string? cnpj = null)
    {
        Name = name ?? Name;
        Cnpj = cnpj ?? Cnpj;
        UpdatedAt = DateTime.UtcNow;
    }
}
