﻿using DataVenia.Modules.Harvey.Domain.Action;
using DataVenia.Modules.Harvey.Infrastructure.Action;
using DataVenia.Modules.Harvey.Infrastructure.Database;
using DataVenia.Modules.Harvey.Application.Abstractions.Data;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Common.Infrastructure.Interceptors;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using DataVenia.Modules.Harvey.Domain.CourtDivision;
using DataVenia.Modules.Harvey.Infrastructure.CourtDivision;
using DataVenia.Modules.Harvey.Domain.Forum;
using DataVenia.Modules.Harvey.Infrastructure.Forum;
using DataVenia.Modules.Harvey.Domain.LegalInstance;
using DataVenia.Modules.Harvey.Infrastructure.LegalInstance;
using DataVenia.Modules.Harvey.Domain.Status;
using DataVenia.Modules.Harvey.Infrastructure.Status;
using DataVenia.Modules.Harvey.Domain.PartyType;
using DataVenia.Modules.Harvey.Infrastructure.PartyType;
using DataVenia.Modules.Harvey.Domain.LegalCategory;
using DataVenia.Modules.Harvey.Infrastructure.LegalCategory;

namespace DataVenia.Modules.Harvey.Infrastructure;

public static class HarveyModule
{
    public static IServiceCollection AddHarveyModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        services.AddInfrastructure(configuration);

        services.AddEndpoints(Presentation.AssemblyReference.Assembly);

        return services;
    }

    private static void AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<HarveyDbContext>((sp, options) =>
            options
                .UseNpgsql(
                    configuration.GetConnectionString("Database"),
                    npgsqlOptions => npgsqlOptions
                        .MigrationsHistoryTable(HistoryRepository.DefaultTableName, Schemas.Harvey))
                .AddInterceptors(sp.GetRequiredService<PublishDomainEventsInterceptor>())
                .UseSnakeCaseNamingConvention());

        services.AddScoped<ILawsuitTypeRepository, LawsuitTypeRepository>();
        services.AddScoped<ICourtDivisionRepository, CourtDivisionRepository>();
        services.AddScoped<IForumRepository, ForumRepository>();
        services.AddScoped<ILegalInstanceRepository, LegalInstanceRepository>();
        services.AddScoped<ILawsuitStatusRepository, LawsuitStatusRepository>();
        services.AddScoped<IPartyTypeRepository, PartyTypeRepository>();
        services.AddScoped<ILegalCategoryRepository, LegalCategoryRepository>();

        services.AddScoped<IUnitOfWork>(sp => sp.GetRequiredService<HarveyDbContext>());
    }
}
