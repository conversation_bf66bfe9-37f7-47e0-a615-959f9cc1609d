﻿using System.Text.Json.Serialization;
using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.LawsuitsData;
using DataVenia.Modules.Lawsuits.Domain.LawsuitSteps;
using LawsuitResponsibleDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitResponsibles.LawsuitResponsible;
using Result = FluentResults.Result;

namespace DataVenia.Modules.Lawsuits.Domain.Lawsuits;



// Informações centralizadas do processo. Informações específicas e que mudam ficam em LawsuitData, no registro do db com created_at mais recent
public sealed class Lawsuit : Entity
{
    private readonly List<LawsuitData> _lawsuitDatas = [];
    private readonly List<MonitoringHistoryEntry> _monitoringHistory = new();
    private readonly List<LawsuitStep> _lawsuitSteps = new();
    private Lawsuit() { }

    public Guid Id { get; private set; }
    public Guid OfficeId { get; private set; }
    public string Cnj { get; private set; } // tirar para case
    
    public string LawsuitTypeId {  get; private set; } // Tipo da ação
    public string LegalCategoryId { get; private set; } // Tipo do processo

    public DateTime DistributedAt { get; private set; } // tirar para case
    public DateTime CreatedAt { get; private set; }
    
    public bool MonitoringEnabled { get; private set; }
    public IReadOnlyCollection<MonitoringHistoryEntry> MonitoringHistory => _monitoringHistory.AsReadOnly();
    public IReadOnlyCollection<LawsuitData> LawsuitDatas => _lawsuitDatas.AsReadOnly();
    public ICollection<LawsuitStep> LawsuitSteps => _lawsuitSteps.AsReadOnly();

    
    public static Lawsuit Create(
                                string cnj,
                                Guid officeId,
                                string lawsuitTypeId,
                                string legalCategoryId,
                                DateTime distributedAt
                                )
    {
        var lawsuit = new Lawsuit
        {
            Id = Guid.NewGuid(),
            OfficeId = officeId,
            Cnj = cnj,
            LawsuitTypeId = lawsuitTypeId,
            LegalCategoryId = legalCategoryId,
            CreatedAt = DateTime.UtcNow,
            DistributedAt = distributedAt
        };

        lawsuit.Raise(new LawsuitCreatedDomainEvent(lawsuit.Id));

        return lawsuit;
    }
    
    /// <summary>
    /// Activates monitoring. Validates that any previous monitoring period is closed.
    /// </summary>
    public FluentResults.Result ActivateMonitoring(Guid userId)
    {
        // If there is an existing monitoring history and its last entry is not closed, return an error.
        if (_monitoringHistory.Any() && _monitoringHistory[^1].StoppedAt == null)
            return Result.Fail(new FluentResults.Error("Monitoring.AlreadyActive").WithMetadata("StatusCode", 400));

        MonitoringEnabled = true;
        _monitoringHistory.Add(new MonitoringHistoryEntry(DateTime.UtcNow, userId));
        return Result.Ok();
    }

    /// <summary>
    /// Deactivates monitoring. Validates that the last monitoring period is still active.
    /// </summary>
    public Result DeactivateMonitoring(Guid userId)
    {
        // If there is no monitoring history or the last entry is already closed, return an error.
        if (!_monitoringHistory.Any() || _monitoringHistory[^1].StoppedAt != null)
            return Result.Fail(new FluentResults.Error("Monitoring.NotActive").WithMetadata("StatusCode", 400));

        MonitoringEnabled = false;
        _monitoringHistory[^1].SetStoppedAt(DateTime.UtcNow, userId);
        return Result.Ok();
    }
}
