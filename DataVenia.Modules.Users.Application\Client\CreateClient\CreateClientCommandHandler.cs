﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Application.Abstractions.Data;
using DataVenia.Modules.Users.Domain.Client;
using Microsoft.Extensions.Logging;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;
namespace DataVenia.Modules.Users.Application.Client.CreateClient;
internal sealed class CreateClientCommandHandler(
    IClientRepository clientRepository,
    IUnitOfWork unitOfWork,
    ILogger<CreateClientCommandHandler> logger)
    : ICommandHandler<CreateClientCommand, Guid>
{
    public async Task<Result<Guid>> Handle(CreateClientCommand request, CancellationToken cancellationToken)
    {
        Result<ClientDomain> clientResult = ClientDomain.Create(
            request.Cpf,
            request.Name,
            request.OfficeId,
            request.LawyerId,
            request.Email
        );

        if (clientResult.IsFailure)
        {
            logger.LogError("Error creating client. {@Error}", clientResult.Error);
            return Result.Failure<Guid>(clientResult.Error);
        }

        clientRepository.Insert(clientResult.Value);

        try
        {
            await unitOfWork.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error saving client to database.");
            return Result.Failure<Guid>(Error.InternalServerError());
        }

        return clientResult.Value.Id;
    }
}
