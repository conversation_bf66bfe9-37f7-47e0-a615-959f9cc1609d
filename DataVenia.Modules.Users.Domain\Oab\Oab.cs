﻿using System.Text.RegularExpressions;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Lawyers;

namespace DataVenia.Modules.Users.Domain.Oab;
public sealed class Oab : Entity
{

    public Guid Id { get; private set; }
    public string Value { get; private set; }
    public Guid LawyerId { get; private set; }
    public Lawyers.Lawyer Lawyer { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    
    // Precompiled regex for performance

    private static readonly Regex OabRegex = new Regex(@"^\d{4,6}/[A-Z]{2}$", RegexOptions.Compiled);
    private Oab() { } // For EF Core

    public static Result<Oab> Create(string value, Guid lawyerId)
    {
        var oab = new Oab()
        {
            Id = Guid.NewGuid(),
            Value = value,
            LawyerId = lawyerId,
            CreatedAt = DateTime.UtcNow
        };

        if (!oab.IsCorrectFormat())
            return Result.Failure<Oab>(new Error("Oab.Invalid", "Provide a valid OAB format.", ErrorType.Validation));

        return Result.Success(oab);
    }
    
    public void UpdateValue(string value)
    {
        Value = value;
        UpdatedAt = DateTime.UtcNow;
    }

    private bool IsCorrectFormat()
    {
        if(string.IsNullOrWhiteSpace(Value))
            return false;

        return OabRegex.IsMatch(Value);
    }
}
