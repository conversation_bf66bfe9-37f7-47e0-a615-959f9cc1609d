﻿using DataVenia.Modules.Lawsuits.Domain.Lawsuits;

namespace DataVenia.Modules.Lawsuits.Domain.LawsuitSteps;

public sealed class LawsuitStep
{
    public Guid Id { get; private set; }
    public string Cnj { get; private set; }
    public string LegalInstanceId { get; private set; }
    public string? Title { get; private set; }
    public string? Description { get; private set; }
    public DateTime OccurredAt { get; private set; }
    public string? ActionBy { get; private set; }
    public bool Secret { get; private set; }
    public DateTime? UpdatedAt { get; private set; }
    public DateTime CreatedAt { get; private set; }
    
    public static LawsuitStep Create(string id, string? title, string? description, string cnj, string legalInstanceId, DateTime occurredAt, string? actionBy, bool secret)
    {
        var lawsuitStep = new LawsuitStep()
        {
            Id = Guid.TryParse(id, out var result) ? result : throw new ArgumentException("Step Id is not a valid Guid"),
            Title = title,
            Description = description,
            Cnj = cnj,
            LegalInstanceId = legalInstanceId,
            OccurredAt = occurredAt,
            ActionBy = actionBy,
            Secret = secret,
            CreatedAt = DateTime.UtcNow
        };

        return lawsuitStep;
    }
}
