﻿using System.Globalization;
using System.Security.Claims;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.ActivateMonitoring;
using DataVenia.Modules.Users.Presentation;
using FluentResults;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;

internal sealed class UpdateMonitoring : IEndpoint
{
    private readonly ILogger<UpdateMonitoring> _logger;

    public UpdateMonitoring(ILogger<UpdateMonitoring> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPatch("/offices/{officeId}/lawsuits/{lawsuitId}", async (Guid officeId, Guid lawsuitId, UpdateMonitoringRequest request, ClaimsPrincipal claims, ISender sender) =>
            {
                try
                {
                    Result result =
                        await sender.Send(new UpdateMonitoringCommand(lawsuitId, claims.GetUserId(), officeId, request.Monitoring));

                    if (result.IsSuccess)
                        return Results.NoContent();
                    else
                    {
                        // Here you could inspect result.Errors and even add a status code
                        // For example:
                        var error = result.Errors.FirstOrDefault();
                        int statusCode = error?.Metadata != null && error.Metadata.TryGetValue("StatusCode", out var codeObj)
                            ? Convert.ToInt32(codeObj, CultureInfo.InvariantCulture)
                            : 500; // default to 500 if no status code is available

                        return Results.Problem(detail: string.Join("; ", result.Errors.Select(e => e.Message)), statusCode: statusCode);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error activating monitoring for lawsuit {LawsuitId}", lawsuitId);
                    return Results.Problem();
                }
            })
            .RequireAuthorization("office:lawsuits:update")
            .WithTags(Tags.Lawsuits);
    }
    
    public sealed record UpdateMonitoringRequest
    {
        public bool Monitoring { get; init; }
    }
}
