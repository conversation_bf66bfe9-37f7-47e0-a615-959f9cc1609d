# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app
COPY . .
ARG BUILD_CONFIGURATION=Release
RUN dotnet restore "./DataVenia.Api/DataVenia.Api.csproj"
RUN dotnet dev-certs https
RUN dotnet build "./DataVenia.Api/DataVenia.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./DataVenia.Api/DataVenia.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM build AS final
WORKDIR /app
EXPOSE 5163
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "DataVenia.Api.dll"]