﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using LawsuitDataDomain = DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData;

namespace DataVenia.Modules.Lawsuits.Infrastructure.LawsuitData;
public sealed class LawsuitDataConfiguration : IEntityTypeConfiguration<LawsuitDataDomain>
{
    public void Configure(EntityTypeBuilder<LawsuitDataDomain> builder)
    {
        builder.ToTable("lawsuit_data");
        
        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasKey(ld => ld.Id);

        builder.HasQueryFilter(u => u.DeletedAt == null);

        builder.Property(ld => ld.Title)
       .IsRequired()
       .HasMaxLength(200);

        builder.Property(ld => ld.Cnj)
       .IsRequired()
       .HasMaxLength(50);

        builder.Property(ld => ld.Court)
               .IsRequired()
               .HasMaxLength(100);

        builder.Property(ld => ld.CourtHref)
               .HasMaxLength(200);
        
        builder.Property(ld => ld.Description)
            .HasMaxLength(500);
        
        builder.Property(ld => ld.Observations)
            .HasMaxLength(500);
        
        builder.Property(ld => ld.IsInstanceCreatedByUser)
            .HasDefaultValue(false);

        builder.HasOne(l => l.Folder)
               .WithMany()
               .HasForeignKey(l => l.FolderId)
               .OnDelete(DeleteBehavior.Restrict);

        // Relacionamento com LawsuitResponsibles (One-to-Many)
        builder.HasMany(l => l.Responsibles)
            .WithOne(lp => lp.LawsuitData)
            .HasForeignKey(ld => ld.LawsuitDataId)
            .OnDelete(DeleteBehavior.NoAction);
        
        // Relacionamento com LawsuitParty (One-to-Many)
        builder.HasMany(l => l.LawsuitParties)
               .WithOne(lp => lp.LawsuitData)
               .HasForeignKey(ld => ld.LawsuitDataId)
               .OnDelete(DeleteBehavior.NoAction);
    }
}
