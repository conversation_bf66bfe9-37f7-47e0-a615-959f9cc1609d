﻿using System.Text.Json;
using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Contracts.Events.LawsuitSync;
using DataVenia.Modules.Lawsuits.Application.Abstractions.Data;
using DataVenia.Modules.Lawsuits.Domain.Lawsuits;
using DataVenia.Modules.Lawsuits.Domain.Outbox;
using MassTransit;
using Microsoft.Extensions.Logging;
using Error = FluentResults.Error;
using Result = FluentResults.Result;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.ActivateMonitoring;

public sealed class UpdateMonitoringCommandHandler(
    ILawsuitRepository lawsuitRepository,
    IUnitOfWork unitOfWork,
    IOutboxRepository outboxRepository,
    IPublishEndpoint publisher,
    ILogger<UpdateMonitoringCommandHandler> logger) : ICommandHandlerFr<UpdateMonitoringCommand>
{
    public async Task<Result> Handle(UpdateMonitoringCommand request, CancellationToken cancellationToken)
        {
            try
            {
                logger.LogInformation("UpdateMonitoringCommandHandler.Handle {LawsuitId}", request.LawsuitId);
                
                var lawsuit = await lawsuitRepository.GetLawsuitByIdAsync(request.LawsuitId, cancellationToken);
                if (lawsuit is null)
                {
                    logger.LogWarning("Lawsuit {LawsuitId} not found.", request.LawsuitId);
                    return Result.Fail(new Error("Lawsuit.NotFound").WithMetadata("StatusCode", 404));                    
                }

                if (lawsuit.OfficeId != request.OfficeId)
                {
                    logger.LogWarning("Lawsuit {LawsuitId} is not associated with office {OfficeId}.", request.LawsuitId, request.OfficeId);
                    return Result.Fail(new Error("Lawsuit.NotFound").WithMetadata("StatusCode", 404));
                }

                if (request.Monitoring)
                {
                    var result = await ActivateMonitoring(lawsuit, request.UserId);
                    if (result.IsFailed)
                        return result;
                }
                else
                {
                    var result = DeactivateMonitoring(lawsuit, request.UserId);
                    if (result.IsFailed)
                        return result;
                }

                lawsuitRepository.Update(lawsuit);
                await unitOfWork.SaveChangesAsync(cancellationToken);
                
                logger.LogInformation("Finished UpdateMonitoringCommandHandler.Handle {LawsuitId}", request.LawsuitId);
                
                return Result.Ok();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Exception while activating monitoring for lawsuit {LawsuitId}", request.LawsuitId);
                return Result.Fail(new Error("Internal.Server.Error").WithMetadata("StatusCode", 500));
            }
        }

    private async Task<Result> ActivateMonitoring(Lawsuit lawsuit, Guid userId)
    {
        // Activate monitoring and handle any domain errors.
        var activationResult = lawsuit.ActivateMonitoring(userId);
        if (activationResult.IsFailed)
        {
            logger.LogError("Failed to activate monitoring for lawsuit {LawsuitId}: {Error}", lawsuit.Id, JsonSerializer.Serialize(activationResult.Errors));
            return Result.Fail(activationResult.Errors);
        }
                    
                    
        // Create the integration event.
        var activateSync = new ActivateLawsuitSync(lawsuit.Cnj, lawsuit.OfficeId);
                    
        // Prepare the outbox message so that persistence and event publication are atomic.
        var outboxMessage = new Outbox
        {
            MessageType = nameof(ActivateLawsuitSync),
            Payload = JsonSerializer.Serialize(activateSync),
            CreatedAt = DateTime.UtcNow,
            ProcessedAt = null
        };
         
        await publisher.Publish(activateSync);
        
        outboxRepository.Insert(outboxMessage);
        
        return Result.Ok();
    }

    private Result DeactivateMonitoring(Lawsuit lawsuit, Guid userId)
    {
        var deactivationResult = lawsuit.DeactivateMonitoring(userId);
        if (deactivationResult.IsFailed)
        {
            logger.LogError("Deactivation failed for lawsuit {LawsuitId}: {Error}",
                lawsuit.Id, string.Join("; ", deactivationResult.Errors.Select(e => e.Message)));
            return Result.Fail(deactivationResult.Errors);
        }
    
        // Publish the deactivation integration event.
        var deactivateSync = new DeactivateLawsuitSync(lawsuit.Cnj, lawsuit.OfficeId);
        var outboxMessage = new Outbox
        {
            MessageType = nameof(DeactivateLawsuitSync),
            Payload = JsonSerializer.Serialize(deactivateSync),
            CreatedAt = DateTime.UtcNow,
            ProcessedAt = null
        };
        outboxRepository.Insert(outboxMessage);

        return Result.Ok();
    }
}
