using DataVenia.Common.Contracts.Events.LawsuitSync;
using DataVenia.Modules.LawsuitSync.Domain.Interfaces;
using MassTransit;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.LawsuitSync.Worker.Workers;

public class ActivateLawsuitSyncConsumer(
    IStartLawsuitMonitoringAppService service,
    ILogger<ActivateLawsuitSyncConsumer> logger) : IConsumer<ActivateLawsuitSync>
{
    public async Task Consume(ConsumeContext<ActivateLawsuitSync> context)
    {
        logger.LogInformation("Start processing activate monitoring logic");

        context = context ?? throw new ArgumentNullException(nameof(context));

        logger.LogInformation("Start processing activate monitoring cnj {Cnj}", context.Message.Cnj);

        await service.ExecuteAsync(context.Message.Cnj, context.Message.SubscriberId);

        logger.LogTrace("Start monitoring cnj {Number}", context.Message.Cnj);
    }
}
