﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\DataVenia.Modules.Notification.Application\DataVenia.Modules.Notification.Application.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="MassTransit">
        <HintPath>..\..\..\..\..\.nuget\packages\masstransit\8.3.0\lib\net8.0\MassTransit.dll</HintPath>
      </Reference>
      <Reference Include="MassTransit.Abstractions">
        <HintPath>..\..\..\..\..\.nuget\packages\masstransit.abstractions\8.3.0\lib\net8.0\MassTransit.Abstractions.dll</HintPath>
      </Reference>
    </ItemGroup>

    <ItemGroup>
      <None Update="Html\confirm-email.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Html\forget-password.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Html\magic-link.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
      <None Update="Html\steps-email.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="MassTransit" Version="8.3.0" />
    </ItemGroup>

</Project>
