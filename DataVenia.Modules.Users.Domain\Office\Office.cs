﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
using DataVenia.Modules.Users.Domain.SharedModels;

namespace DataVenia.Modules.Users.Domain.Office;

public sealed class Office : Entity
{
    private readonly List<OfficeUser> _officeUsers = [];
    private readonly List<Contact> _contacts = [];
    private readonly List<Address> _addresses = [];
    private Office() { }

    public Guid Id { get; private set; }
    public string Name { get; private set; }
    public string Website { get; private set; }
    public string Cnpj { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public IReadOnlyCollection<OfficeUser> OfficeUsers => _officeUsers.ToList();
    public IReadOnlyCollection<Contact> Contacts => _contacts.ToList();
    public IReadOnlyCollection<Address> Addresses => _addresses.ToList();

    public static Office Create(string name, string website, string cnpj, IReadOnlyCollection<Contact>? contacts
        , IReadOnlyCollection<Address>? addresses
        )
    {
        var office = new Office()
        {
            Id = Guid.NewGuid(),
            Name = name,
            Website = website,
            Cnpj = cnpj,
            CreatedAt = DateTime.UtcNow
        };

        office._contacts.AddRange(contacts ?? new List<Contact>());
        office._addresses.AddRange(addresses ?? new List<Address>());

        return office;
    }
}
