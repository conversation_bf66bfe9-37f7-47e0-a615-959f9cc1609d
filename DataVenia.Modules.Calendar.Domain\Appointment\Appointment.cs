﻿using DataVenia.Common.Domain;
using AppointmentParticipantDomain = DataVenia.Modules.Calendar.Domain.AppointmentParticipant.AppointmentParticipant;
using StatusDomain = DataVenia.Modules.Calendar.Domain.Status.Status;

namespace DataVenia.Modules.Calendar.Domain.Appointments;

public sealed class Appointment : Entity
{
    private readonly List<AppointmentParticipantDomain> _participants = new();
    private readonly List<TimeSpan> _alerts = [];

    public Guid Id { get; private set; }
    public string Type { get; private set; }

    public string Name { get; private set; }
    public string Description { get; private set; }
    public Guid ResponsibleLawyerId { get; private set; }
    public Recurrence? Recurrence { get; private set; }
    public Guid OwnerLawyerId { get; private set; }
    public Guid OwnerOfficeId { get; private set; }
    public Guid? StatusId { get; private set; }
    public StatusDomain Status { get; private set; }
    public IReadOnlyCollection<TimeSpan> Alerts => _alerts.AsReadOnly();
    public IReadOnlyCollection<AppointmentParticipantDomain> Participants => _participants.AsReadOnly();
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private Appointment() { }

    public static Result<Appointment> Create(string type, string name, string description, Guid responsibleLawyerId,
        Recurrence? recurrence, Guid ownerLawyerId, Guid ownerOfficeId, List<Guid> participantLawyersIds,
        List<TimeSpan> alerts, Guid? statusId)
    {
        Appointment appointment = new()
        {
            Id = Guid.NewGuid(),
            Type = type,
            Name = name,
            Description = description,
            ResponsibleLawyerId = responsibleLawyerId,
            Recurrence = recurrence,
            OwnerLawyerId = ownerLawyerId,
            OwnerOfficeId = ownerOfficeId,
            StatusId = statusId,
            CreatedAt = DateTime.UtcNow
        };

        foreach (Guid lawyerId in participantLawyersIds)
        {
            appointment._participants.Add(
                AppointmentParticipantDomain.Create(appointment.Id, lawyerId).Value);
        }

        appointment._alerts.AddRange(alerts);

        return appointment;
    }

    public void Update(string type, string name, string description, Guid responsibleLawyerId, Recurrence? recurrence,
        List<Guid> participantLawyersIds, List<TimeSpan> alerts, Guid statusId)
    {
        Type = type;
        Name = name;
        Description = description;
        ResponsibleLawyerId = responsibleLawyerId;
        Recurrence = recurrence;
        StatusId = statusId;
        UpdatedAt = DateTime.UtcNow;

        _participants.Clear();
        foreach (Guid lawyerId in participantLawyersIds)
        {
            _participants.Add(
                AppointmentParticipantDomain.Create(Id, lawyerId).Value);
        }


        _alerts.Clear();
        _alerts.AddRange(alerts);
    }

    public void UpdatePartial(
        string? type,
        string? name,
        string? description,
        Guid? responsibleLawyerId,
        Recurrence? recurrence,
        List<Guid>? participantLawyersIds,
        List<TimeSpan>? alerts,
        Guid? statusId)
    {
        UpdatedAt = DateTime.UtcNow;
        
        if (type is not null)
            Type = type;

        if (name is not null)
            Name = name;

        if (description is not null)
            Description = description;

        if (responsibleLawyerId.HasValue)
            ResponsibleLawyerId = responsibleLawyerId.Value;

        if (recurrence is not null)
            Recurrence = recurrence;

        if (statusId.HasValue)
            StatusId = statusId.Value;

        if (participantLawyersIds is not null)
        {
            _participants.Clear();
            foreach (Guid lawyerId in participantLawyersIds)
            {
                _participants.Add(
                    AppointmentParticipantDomain.Create(Id, lawyerId).Value);
            }
        }
        
        if (alerts is null)
            return;

        _alerts.Clear();
        _alerts.AddRange(alerts);
    }
}
