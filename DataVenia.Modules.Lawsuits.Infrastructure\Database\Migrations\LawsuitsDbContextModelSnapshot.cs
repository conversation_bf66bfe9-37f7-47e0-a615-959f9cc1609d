﻿// <auto-generated />
using System;
using System.Collections.Generic;
using DataVenia.Modules.Lawsuits.Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DataVenia.Modules.Lawsuits.Infrastructure.Database.Migrations
{
    [DbContext(typeof(LawsuitsDbContext))]
    partial class LawsuitsDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("lawsuit")
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Application.Cases.GetCases.CaseResponse", b =>
                {
                    b.Property<string>("Access")
                        .HasColumnType("text")
                        .HasColumnName("access");

                    b.Property<decimal?>("CauseValue")
                        .HasColumnType("numeric")
                        .HasColumnName("cause_value");

                    b.Property<decimal?>("ConvictionValue")
                        .HasColumnType("numeric")
                        .HasColumnName("conviction_value");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<Guid?>("FolderId")
                        .HasColumnType("uuid")
                        .HasColumnName("folder_id");

                    b.Property<string>("FolderName")
                        .HasColumnType("text")
                        .HasColumnName("folder_name");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Observations")
                        .HasColumnType("text")
                        .HasColumnName("observations");

                    b.Property<List<Guid>>("ResponsibleIds")
                        .IsRequired()
                        .HasColumnType("uuid[]")
                        .HasColumnName("responsible_ids");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.ToTable("case_response", "lawsuit", t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits.LawsuitResponse", b =>
                {
                    b.Property<string>("Access")
                        .HasColumnType("text")
                        .HasColumnName("access");

                    b.Property<decimal?>("CauseValue")
                        .HasColumnType("numeric")
                        .HasColumnName("cause_value");

                    b.Property<string>("Cnj")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("cnj");

                    b.Property<decimal?>("ConvictionValue")
                        .HasColumnType("numeric")
                        .HasColumnName("conviction_value");

                    b.Property<string>("Court")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("court");

                    b.Property<string>("CourtDivisionId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("court_division_id");

                    b.Property<string>("CourtHref")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("court_href");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<DateTime>("DistributedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("distributed_at");

                    b.Property<Guid?>("EvolvedFromCaseId")
                        .HasColumnType("uuid")
                        .HasColumnName("evolved_from_case_id");

                    b.Property<Guid?>("FolderId")
                        .HasColumnType("uuid")
                        .HasColumnName("folder_id");

                    b.Property<string>("FolderName")
                        .HasColumnType("text")
                        .HasColumnName("folder_name");

                    b.Property<string>("ForumId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("forum_id");

                    b.Property<Guid?>("GroupingCaseId")
                        .HasColumnType("uuid")
                        .HasColumnName("grouping_case_id");

                    b.Property<Guid>("Id")
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("LawsuitStatusId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("lawsuit_status_id");

                    b.Property<string>("LawsuitTypeId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("lawsuit_type_id");

                    b.Property<string>("LegalCategoryId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("legal_category_id");

                    b.Property<string>("LegalInstanceId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("legal_instance_id");

                    b.Property<string>("Observations")
                        .HasColumnType("text")
                        .HasColumnName("observations");

                    b.Property<List<Guid>>("ResponsibleIds")
                        .IsRequired()
                        .HasColumnType("uuid[]")
                        .HasColumnName("responsible_ids");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.ToTable("lawsuit_response", "lawsuit", t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.Cases.Case", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("NOW()");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<Guid>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.HasKey("Id")
                        .HasName("pk_case");

                    b.ToTable("case", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.CasesData.CaseData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Access")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("access");

                    b.Property<Guid>("CaseId")
                        .HasColumnType("uuid")
                        .HasColumnName("case_id");

                    b.Property<decimal>("CauseValue")
                        .HasColumnType("numeric")
                        .HasColumnName("cause_value");

                    b.Property<decimal?>("ConvictionValue")
                        .HasColumnType("numeric")
                        .HasColumnName("conviction_value");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<Guid?>("FolderId")
                        .HasColumnType("uuid")
                        .HasColumnName("folder_id");

                    b.Property<string>("Observations")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("observations");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.HasKey("Id")
                        .HasName("pk_case_data");

                    b.HasIndex("CaseId")
                        .HasDatabaseName("ix_case_data_case_id");

                    b.HasIndex("FolderId")
                        .HasDatabaseName("ix_case_data_folder_id");

                    b.ToTable("case_data", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.CasesParties.CaseParty", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("CaseDataId")
                        .HasColumnType("uuid")
                        .HasColumnName("case_data_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsClient")
                        .HasColumnType("boolean")
                        .HasColumnName("is_client");

                    b.Property<Guid>("PartyId")
                        .HasColumnType("uuid")
                        .HasColumnName("party_id");

                    b.Property<string>("PartyType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("party_type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_case_party");

                    b.HasIndex("CaseDataId", "PartyId")
                        .IsUnique()
                        .HasDatabaseName("ix_case_party_case_data_id_party_id");

                    b.ToTable("case_party", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.CasesResponsibles.CaseResponsible", b =>
                {
                    b.Property<Guid>("CaseDataId")
                        .HasColumnType("uuid")
                        .HasColumnName("case_data_id");

                    b.Property<Guid>("LawyerId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawyer_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("CaseDataId", "LawyerId")
                        .HasName("pk_case_responsible");

                    b.ToTable("case_responsible", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.DTOs.PartyDto", b =>
                {
                    b.Property<bool>("IsClient")
                        .HasColumnType("boolean")
                        .HasColumnName("is_client");

                    b.Property<string>("PartyType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("party_type");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid")
                        .HasColumnName("user_id");

                    b.ToTable("party_dto", "lawsuit", t =>
                        {
                            t.ExcludeFromMigrations();
                        });
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.Folder.Folder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("color");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("name");

                    b.Property<Guid?>("ParentFolderId")
                        .HasColumnType("uuid")
                        .HasColumnName("parent_folder_id");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_folder");

                    b.HasIndex("ParentFolderId")
                        .HasDatabaseName("ix_folder_parent_folder_id");

                    b.ToTable("folder", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitParties.LawsuitParty", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<bool>("IsClient")
                        .HasColumnType("boolean")
                        .HasColumnName("is_client");

                    b.Property<Guid>("LawsuitDataId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawsuit_data_id");

                    b.Property<Guid>("PartyId")
                        .HasColumnType("uuid")
                        .HasColumnName("party_id");

                    b.Property<string>("PartyType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("party_type");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_party");

                    b.HasIndex("LawsuitDataId", "PartyId")
                        .IsUnique()
                        .HasDatabaseName("ix_lawsuit_party_lawsuit_data_id_party_id");

                    b.ToTable("lawsuit_party", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitResponsibles.LawsuitResponsible", b =>
                {
                    b.Property<Guid>("LawsuitDataId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawsuit_data_id");

                    b.Property<Guid>("LawyerId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawyer_id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("LawsuitDataId", "LawyerId")
                        .HasName("pk_lawsuit_responsible");

                    b.ToTable("lawsuit_responsible", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitSteps.LawsuitStep", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ActionBy")
                        .HasColumnType("text")
                        .HasColumnName("action_by");

                    b.Property<string>("Cnj")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("text")
                        .HasColumnName("cnj");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("NOW()");

                    b.Property<string>("Description")
                        .HasColumnType("text")
                        .HasColumnName("description");

                    b.Property<Guid?>("LawsuitId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawsuit_id");

                    b.Property<string>("LegalInstanceId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("text")
                        .HasColumnName("legal_instance_id");

                    b.Property<DateTime>("OccurredAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("occurred_at");

                    b.Property<bool>("Secret")
                        .HasColumnType("boolean")
                        .HasColumnName("secret");

                    b.Property<string>("Title")
                        .HasColumnType("text")
                        .HasColumnName("title");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated_at");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_step");

                    b.HasIndex("LawsuitId")
                        .HasDatabaseName("ix_lawsuit_step_lawsuit_id");

                    b.ToTable("lawsuit_step", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.Lawsuits.Lawsuit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Cnj")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("cnj");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at")
                        .HasDefaultValueSql("NOW()");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<DateTime>("DistributedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("distributed_at");

                    b.Property<string>("LawsuitTypeId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("lawsuit_type_id");

                    b.Property<string>("LegalCategoryId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("legal_category_id");

                    b.Property<bool>("MonitoringEnabled")
                        .HasColumnType("boolean")
                        .HasColumnName("monitoring_enabled");

                    b.Property<string>("MonitoringHistory")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("jsonb")
                        .HasColumnName("monitoring_history")
                        .HasDefaultValueSql("'[]'::jsonb");

                    b.Property<Guid>("OfficeId")
                        .HasColumnType("uuid")
                        .HasColumnName("office_id");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit");

                    b.ToTable("lawsuit", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Access")
                        .HasColumnType("text")
                        .HasColumnName("access");

                    b.Property<decimal>("CauseValue")
                        .HasColumnType("numeric")
                        .HasColumnName("cause_value");

                    b.Property<string>("Cnj")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)")
                        .HasColumnName("cnj");

                    b.Property<decimal?>("ConvictionValue")
                        .HasColumnType("numeric")
                        .HasColumnName("conviction_value");

                    b.Property<string>("Court")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)")
                        .HasColumnName("court");

                    b.Property<string>("CourtDivisionId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("court_division_id");

                    b.Property<string>("CourtHref")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("court_href");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("deleted_at");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("description");

                    b.Property<Guid?>("EvolvedFromCaseId")
                        .HasColumnType("uuid")
                        .HasColumnName("evolved_from_case_id");

                    b.Property<Guid?>("FolderId")
                        .HasColumnType("uuid")
                        .HasColumnName("folder_id");

                    b.Property<string>("ForumId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("forum_id");

                    b.Property<Guid?>("GroupingCaseId")
                        .HasColumnType("uuid")
                        .HasColumnName("grouping_case_id");

                    b.Property<bool>("IsInstanceCreatedByUser")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false)
                        .HasColumnName("is_instance_created_by_user");

                    b.Property<Guid>("LawsuitId")
                        .HasColumnType("uuid")
                        .HasColumnName("lawsuit_id");

                    b.Property<string>("LawsuitStatusId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("lawsuit_status_id");

                    b.Property<string>("LegalInstanceId")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("legal_instance_id");

                    b.Property<string>("Observations")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)")
                        .HasColumnName("observations");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)")
                        .HasColumnName("title");

                    b.HasKey("Id")
                        .HasName("pk_lawsuit_data");

                    b.HasIndex("FolderId")
                        .HasDatabaseName("ix_lawsuit_data_folder_id");

                    b.HasIndex("LawsuitId")
                        .HasDatabaseName("ix_lawsuit_data_lawsuit_id");

                    b.ToTable("lawsuit_data", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.Outbox.Outbox", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created_at");

                    b.Property<string>("MessageType")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("message_type");

                    b.Property<string>("Payload")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("payload");

                    b.Property<bool>("Processed")
                        .HasColumnType("boolean")
                        .HasColumnName("processed");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("processed_at");

                    b.HasKey("Id")
                        .HasName("pk_outbox");

                    b.HasIndex("Processed")
                        .HasDatabaseName("ix_outbox_processed");

                    b.ToTable("outbox", "lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.CasesData.CaseData", b =>
                {
                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.Cases.Case", "Case")
                        .WithMany("CaseDatas")
                        .HasForeignKey("CaseId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_case_data_case_case_id");

                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.Folder.Folder", "Folder")
                        .WithMany()
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_case_data_folder_folder_id");

                    b.Navigation("Case");

                    b.Navigation("Folder");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.CasesParties.CaseParty", b =>
                {
                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.CasesData.CaseData", "CaseData")
                        .WithMany("CaseParties")
                        .HasForeignKey("CaseDataId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_case_party_case_data_case_data_id");

                    b.Navigation("CaseData");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.CasesResponsibles.CaseResponsible", b =>
                {
                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.CasesData.CaseData", "CaseData")
                        .WithMany("Responsibles")
                        .HasForeignKey("CaseDataId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_case_responsible_case_datas_case_data_id");

                    b.Navigation("CaseData");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.Folder.Folder", b =>
                {
                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.Folder.Folder", "ParentFolder")
                        .WithMany()
                        .HasForeignKey("ParentFolderId")
                        .HasConstraintName("fk_folder_folder_parent_folder_id");

                    b.Navigation("ParentFolder");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitParties.LawsuitParty", b =>
                {
                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData", "LawsuitData")
                        .WithMany("LawsuitParties")
                        .HasForeignKey("LawsuitDataId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_lawsuit_party_lawsuit_data_lawsuit_data_id");

                    b.Navigation("LawsuitData");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitResponsibles.LawsuitResponsible", b =>
                {
                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData", "LawsuitData")
                        .WithMany("Responsibles")
                        .HasForeignKey("LawsuitDataId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_lawsuit_responsible_lawsuit_datas_lawsuit_data_id");

                    b.Navigation("LawsuitData");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitSteps.LawsuitStep", b =>
                {
                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.Lawsuits.Lawsuit", null)
                        .WithMany("LawsuitSteps")
                        .HasForeignKey("LawsuitId")
                        .HasConstraintName("fk_lawsuit_step_lawsuit_lawsuit_id");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData", b =>
                {
                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.Folder.Folder", "Folder")
                        .WithMany()
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_lawsuit_data_folder_folder_id");

                    b.HasOne("DataVenia.Modules.Lawsuits.Domain.Lawsuits.Lawsuit", "Lawsuit")
                        .WithMany("LawsuitDatas")
                        .HasForeignKey("LawsuitId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired()
                        .HasConstraintName("fk_lawsuit_data_lawsuit_lawsuit_id");

                    b.Navigation("Folder");

                    b.Navigation("Lawsuit");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.Cases.Case", b =>
                {
                    b.Navigation("CaseDatas");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.CasesData.CaseData", b =>
                {
                    b.Navigation("CaseParties");

                    b.Navigation("Responsibles");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.Lawsuits.Lawsuit", b =>
                {
                    b.Navigation("LawsuitDatas");

                    b.Navigation("LawsuitSteps");
                });

            modelBuilder.Entity("DataVenia.Modules.Lawsuits.Domain.LawsuitsData.LawsuitData", b =>
                {
                    b.Navigation("LawsuitParties");

                    b.Navigation("Responsibles");
                });
#pragma warning restore 612, 618
        }
    }
}
