﻿using DataVenia.Common.Domain;
using DataVenia.Modules.Lawsuits.Domain.CasesData;

namespace DataVenia.Modules.Lawsuits.Domain.Cases;

public class Case : Entity
{
    private readonly List<CaseData> _caseDatas = [];
    private Case() { }

    public Guid Id { get; private set; }
    public Guid OfficeId { get; private set; }
    public DateTime CreatedAt { get; private set; }

    public IReadOnlyCollection<CaseData> CaseDatas => _caseDatas.AsReadOnly();
    
    public static Case Create(
        Guid officeId
        )
    {
        var @case = new Case
        {
            Id = Guid.NewGuid(),
            OfficeId = officeId,
            CreatedAt = DateTime.UtcNow
        };

        // @case.Raise(new CaseCreatedDomainEvent(@case.Id));

        return @case;
    }
}


