﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Common.Domain;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
internal sealed class GetLawsuitsQueryHandler(ILawsuitRepository lawsuitRepository)
    : IQueryHandler<GetLawsuitsQuery, IReadOnlyCollection<LawsuitResponse>>
{
    public async Task<Result<IReadOnlyCollection<LawsuitResponse>>> <PERSON><PERSON>(GetLawsuitsQuery request, CancellationToken cancellationToken)
    {
        Result<IReadOnlyCollection<LawsuitResponse>> lawsuits = await lawsuitRepository.GetLawsuitsAsync(request.userId, request.officeId, cancellationToken: cancellationToken);

        return lawsuits;
    }
}
