using DataVenia.Common.Domain;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Users.Application.User.Login;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Hosting;

namespace DataVenia.Modules.Users.Presentation.User;

public sealed class Login : IEndpoint
{
    private readonly IWebHostEnvironment _environment;
    public Login(IWebHostEnvironment environment)
    {
        _environment = environment;
    }
    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        routeBuilder.MapPost("login", async (HttpRequest request, ISender sender, HttpContext http) =>
        {
            if (!request.HasFormContentType)
                return Results.BadRequest("Content type must be x-www-form-urlencoded.");

            IFormCollection form = await request.ReadFormAsync();
            string username = form["Username"];
            string password = form["Password"];
            
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                return RedirectWithError(username);
            
            Result<LoginResponse> result = await sender.Send(new LoginCommand(
                username!,
                password!));

            var productionCookieDomain = ".datavenia.io";
            var productionDomain = "https://app.datavenia.io";
            
            return result.Match(
                loginResponse =>
                {
                    var IsDevelopment = _environment.IsDevelopment();
                    
                    http.Response.Cookies.Append("AccessToken", loginResponse.AccessToken, new CookieOptions
                    {
                        HttpOnly = true,                                // Torna o cookie inacessível ao JavaScript
                        Secure = true,                                  // Garante que o cookie só seja enviado por conexões HTTPS
                        SameSite = SameSiteMode.Strict,                 // Restringe o envio do cookie apenas em navegações do mesmo domínio
                        Expires = DateTime.UtcNow.AddDays(7),        // Tempo de expiração
                        Domain = IsDevelopment ? "localhost" : productionCookieDomain
                    });

                    // Configurar Refresh Token como cookie HttpOnly
                    http.Response.Cookies.Append("RefreshToken", loginResponse.RefreshToken, new CookieOptions
                    {
                        HttpOnly = true,
                        Secure = true,
                        SameSite = SameSiteMode.Strict,
                        Expires = DateTime.UtcNow.AddDays(7),
                        Domain = IsDevelopment ? "localhost" : productionCookieDomain
                    });         

                    return Results.Redirect(IsDevelopment ? "/" : productionDomain, permanent: false, preserveMethod: false);
                },
                problem =>
                {
                    // Compute base64 of username
                    return RedirectWithError(username, productionDomain);
                }
            );
        })
        .AllowAnonymous()
        .WithTags(Tags.Users);
    }

    private static IResult RedirectWithError(string? username, string? domain = null)
    {
        return Results.Redirect(
            string.IsNullOrEmpty(username)
                ? $"{domain}/login?auth=error"
                : $"{domain}/login?auth=error&state={Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(username))}",
            permanent: false,
            preserveMethod: false);
    }
    
    public sealed record LoginRequest
    {
        public string Username { get; init; }
        public string Password { get; init; }
    }
}
