﻿<Project Sdk="Microsoft.NET.Sdk">
  <ItemGroup>
    <ProjectReference Include="..\DataVenia.Common.Application\DataVenia.Common.Application.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.Lawsuits.Infrastructure\DataVenia.Modules.Lawsuits.Infrastructure.csproj" />
    <ProjectReference Include="..\DataVenia.Modules.LawsuitSync.Domain\DataVenia.Modules.LawsuitSync.Domain.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="FluentResults">
      <HintPath>..\..\..\..\.nuget\packages\fluentresults\3.16.0\lib\netstandard2.1\FluentResults.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Helpers\" />
  </ItemGroup>
</Project>
