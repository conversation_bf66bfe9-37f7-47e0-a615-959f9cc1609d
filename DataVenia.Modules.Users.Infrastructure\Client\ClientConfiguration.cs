using DataVenia.Modules.Users.Domain.SharedModels;
using AddressDomain = DataVenia.Modules.Users.Domain.SharedModels.Address;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ClientDomain = DataVenia.Modules.Users.Domain.Client.Client;
namespace DataVenia.Modules.Users.Infrastructure.Client;

internal sealed class ClientConfiguration : IEntityTypeConfiguration<ClientDomain>
{
    public void Configure(EntityTypeBuilder<ClientDomain> builder)
    {
        builder.ToTable("client");

        builder.Property(x => x.Id).HasColumnType("uuid");
        builder.HasQueryFilter(u => u.DeletedAt == null);
        
        builder.HasKey(ou => ou.Id);
        
        // Configure CPF as required
        builder.Property(c => c.Cpf)
            .HasMaxLength(11)
            .IsRequired();

        // Configure Name as required
        builder.Property(c => c.Name)
            .HasMaxLength(200)
            .IsRequired();
        
        // // Relacionamento com Contacts
        // builder.HasMany(c => c.Contacts)
        //     .WithOne()
        //     .HasForeignKey("ClientId") // FK na tabela Contacts
        //     .OnDelete(DeleteBehavior.NoAction);
        //
        // // Relacionamento com Addresses
        // builder.HasMany(c => c.Addresses)
        //     .WithOne()
        //     .HasForeignKey("ClientId") // FK na tabela Addresses
        //     .OnDelete(DeleteBehavior.NoAction);
    }
}
