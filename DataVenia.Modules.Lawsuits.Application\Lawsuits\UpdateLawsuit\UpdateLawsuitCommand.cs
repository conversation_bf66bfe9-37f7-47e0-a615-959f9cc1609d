﻿using DataVenia.Common.Application.Messaging;
using DataVenia.Modules.Lawsuits.Domain.DTOs;

namespace DataVenia.Modules.Lawsuits.Application.Lawsuits.UpdateLawsuit;
public sealed record UpdateLawsuitCommand(
    Guid UrlLawsuitId,
    string Title,
    Guid LawsuitId,
    Guid? FolderId,
    string LegalInstanceId,
    string LawsuitStatusId,
    // List<PartyDto> Parties,
    string Court,
    string CourtDivisionId,
    string ForumId,
    string CourtHref,
    string Description,
    string Observations,
    decimal CauseValue,
    decimal ConvictionValue,
    List<Guid> ResponsibleIds,
    string Access,
    Guid? EvolvedFromCaseId,
    Guid? GroupingCaseId,
    Guid LawyerId,
    Guid OfficeId
): ICommand;
