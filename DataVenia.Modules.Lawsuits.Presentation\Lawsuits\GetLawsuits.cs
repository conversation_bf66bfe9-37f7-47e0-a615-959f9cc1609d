﻿using System.Security.Claims;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using DataVenia.Common.Domain;
using DataVenia.Common.Infrastructure.Authentication;
using DataVenia.Common.Presentation.ApiResults;
using DataVenia.Common.Presentation.Endpoints;
using DataVenia.Modules.Lawsuits.Application.Lawsuits.GetLawsuits;
using DataVenia.Modules.Users.Presentation;
using Microsoft.Extensions.Logging;

namespace DataVenia.Modules.Lawsuits.Presentation.Lawsuits;

internal sealed class GetLawsuits : IEndpoint
{
    private readonly ILogger<GetLawsuits> _logger;

    public GetLawsuits(ILogger<GetLawsuits> logger)
    {
        _logger = logger;
    }

    public void MapEndpoint(IEndpointRouteBuilder routeBuilder)
    {
        // passar officeId
        routeBuilder.MapGet("/offices/{officeId}/lawsuits", async (Guid officeId, ClaimsPrincipal claims, ISender sender) =>
        {
            try
            {
                // tem que usar o office_id da empresa que o cara ta logado
                Result<IReadOnlyCollection<LawsuitResponse>> result = await sender.Send(new GetLawsuitsQuery(claims.GetUserId(), officeId));

                return result.Match(
                   success =>
                   {
                       var responseObject = new
                       {
                           items = success
                       };

                       return Results.Ok(responseObject);
                   },
                   ApiResults.Problem);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting lawsuits");
            }

            return Results.NotFound();
        })
        .RequireAuthorization("office:lawsuits:read")
        .WithTags(Tags.Lawsuits);
    }
}
