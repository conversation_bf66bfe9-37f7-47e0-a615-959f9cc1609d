﻿using DataVenia.Common.Contracts.OfficeLawyer;
using DataVenia.Common.Domain;
using DataVenia.Modules.Users.Domain.Authorization;
using DataVenia.Modules.Users.Domain.Lawyers;
using DataVenia.Modules.Users.Domain.Users;

namespace DataVenia.Modules.Users.Domain.IntermediateClasses.OfficeLawyer;
public sealed class OfficeUser: Entity
{
    public Guid Id { get; private set; }
    public Guid OfficeId { get; private set; }
    public Office.Office Office { get; private set; }

    public Guid UserId { get; private set; }
    public Users.User User { get; private set; }

    public Guid OwnerId { get; private set; }
    public Lawyers.Lawyer Owner { get; private set; }

    public string RoleName { get; private set; }
    public Role Role { get; private set; }
    public InvitationStatus InvitationStatus { get; private set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }

    private OfficeUser() { }
    public static OfficeUser Create(Guid officeId, Guid userId, Guid ownerId, string memberRole)
    {
        var officeLawyer = new OfficeUser()
        {
            Id = Guid.NewGuid(),
            OfficeId = officeId,
            UserId = userId,
            OwnerId = ownerId,
            RoleName = memberRole,
            InvitationStatus = InvitationStatus.PENDING,
            CreatedAt = DateTime.UtcNow
        };

        return officeLawyer;
    }

    public static Result<OfficeUser> CreateClient(Guid officeId, Guid clientId, Guid ownerId, string clientRole)
    {
        var officeClient = new OfficeUser()
        {
            Id = Guid.NewGuid(),
            OfficeId = officeId,
            UserId = clientId,
            OwnerId = ownerId,
            RoleName = clientRole,
            InvitationStatus = InvitationStatus.ACTIVE
        };

        return officeClient;
    }

    public void Accept()
    {
        if (InvitationStatus == InvitationStatus.PENDING)
            InvitationStatus = InvitationStatus.ACTIVE;
    }
}

